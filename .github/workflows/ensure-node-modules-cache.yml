name: Ensure node modules cache

on:
  push:
    branches:
      - main

permissions:
  contents: read

jobs:
  linux:
    name: Linux
    runs-on: [ self-hosted, 1ES.Pool=1es-vscode-ubuntu-22.04-x64 ]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.x'

      - name: Restore build cache
        uses: actions/cache@v4
        id: build-cache
        with:
          key: build_cache-${{ hashFiles('build/.cachesalt', 'package-lock.json') }}
          path: .build/build_cache

      - name: Install dependencies
        if: steps.build-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: Create build cache archive
        if: steps.build-cache.outputs.cache-hit != 'true'
        run: |
          set -e
          mkdir -p .build
          node build/listBuildCacheFiles.js .build/build_cache_list.txt
          mkdir -p .build/build_cache
          tar -czf .build/build_cache/cache.tgz --files-from .build/build_cache_list.txt

  windows:
    name: Windows
    runs-on: [ self-hosted, 1ES.Pool=1es-vscode-windows-2022-x64 ]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22.14.x'

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'
          architecture: 'x64'

      - name: Restore build cache
        uses: actions/cache@v4
        id: build-cache
        with:
          key: windows-build_cache-${{ hashFiles('build/.cachesalt', 'package-lock.json') }}
          path: .build/build_cache

      - name: Install dependencies
        if: steps.build-cache.outputs.cache-hit != 'true'
        run: npm ci

      - name: Create build cache archive
        if: steps.build-cache.outputs.cache-hit != 'true'
        run: |
          mkdir -Force .build
          node build/listBuildCacheFiles.js .build/build_cache_list.txt
          mkdir -Force .build/build_cache
          7z.exe a .build/build_cache/cache.7z -mx3 `@.build/build_cache_list.txt
