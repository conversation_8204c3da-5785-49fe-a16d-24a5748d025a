{"workspaceFoldersFilePaths": ["../../../vscode-copilot"], "workspaceFolderFilePath": "../../../vscode-copilot", "symbols": [], "activeFileDiagnostics": [], "debugConsoleOutput": "", "terminalBuffer": "", "terminalLastCommand": {"commandLine": "", "cwd": "/Users/<USER>/Code/Work/vscode-copilot", "output": "penlv@Pengs-MBP vscode-copilot % \n"}, "terminalSelection": "", "terminalShellType": "bash", "repoContexts": [{"rootUri": {"$mid": 1, "path": "/Users/<USER>/Code/Work/vscode-copilot", "scheme": "file"}, "headBranchName": "main", "headCommitHash": "f671cb2537f45fc3145c9a3fd54b1bd25fb409f4", "upstreamBranchName": "main", "upstreamRemote": "origin", "isRebasing": true, "remotes": ["origin"], "remoteFetchUrls": ["**************:microsoft/vscode-copilot.git"], "changes": {"mergeChanges": [], "indexChanges": [], "workingTree": [], "untrackedChanges": []}}], "notebookDocumentFilePaths": [], "textDocumentFilePaths": []}