{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Ex2 - Getting and Knowing your Data\n", "\n", "Check out [Chipotle Exercises Video Tutorial](https://www.youtube.com/watch?v=lpuYZ5EUyS8&list=PLgJhDSE2ZLxaY_DigHeiIDC1cD09rXgJv&index=2) to watch a data scientist go through the exercises"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This time we are going to pull data directly from the internet.\n", "Special thanks to: https://github.com/justmarkham for sharing the dataset and materials.\n", "\n", "### Step 1. Import the necessary libraries"]}, {"cell_type": "code", "execution_count": 56, "metadata": {"collapsed": false}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 2. Import the dataset from this [address](https://raw.githubusercontent.com/justmarkham/DAT8/master/data/chipotle.tsv). "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 3. Assign it to a variable called chipo."]}, {"cell_type": "code", "execution_count": 57, "metadata": {"collapsed": false}, "outputs": [], "source": ["url = \"https://raw.githubusercontent.com/justmarkham/DAT8/master/data/chipotle.tsv\"\n", "\n", "chipo = pd.read_csv(url, sep=\"\\t\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 4. See the first 10 entries"]}, {"cell_type": "code", "execution_count": 58, "metadata": {"collapsed": false, "scrolled": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td><PERSON>s and Fresh <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Izze</td>\n", "      <td>[<PERSON><PERSON>]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Nantucket Nectar</td>\n", "      <td>[Apple]</td>\n", "      <td>$3.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>Chips and Tomatillo-Green <PERSON></td>\n", "      <td>NaN</td>\n", "      <td>$2.39</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Chicken Bowl</td>\n", "      <td>[<PERSON> (Mild), [Rice, Cheese, Sou...</td>\n", "      <td>$10.98</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>3</td>\n", "      <td>1</td>\n", "      <td>Side of Chips</td>\n", "      <td>NaN</td>\n", "      <td>$1.69</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[Tomatillo Red Chili Salsa, [Fajita Vegetables...</td>\n", "      <td>$11.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>Steak Soft Tacos</td>\n", "      <td>[Tomatillo <PERSON>, [<PERSON><PERSON>, Ch...</td>\n", "      <td>$9.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>Steak Burrito</td>\n", "      <td>[<PERSON>, [<PERSON>, <PERSON> Beans, Pinto...</td>\n", "      <td>$9.25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   order_id  quantity                              item_name  \\\n", "0         1         1           Chips and Fresh Tomato Salsa   \n", "1         1         1                                   Izze   \n", "2         1         1                       Nantucket Nectar   \n", "3         1         1  Chips and Tomatillo-Green Chili Salsa   \n", "4         2         2                           Chicken Bowl   \n", "5         3         1                           Chicken Bowl   \n", "6         3         1                          Side of Chips   \n", "7         4         1                          Steak Burrito   \n", "8         4         1                       Steak Soft Tacos   \n", "9         5         1                          Steak Burrito   \n", "\n", "                                  choice_description item_price  \n", "0                                                NaN     $2.39   \n", "1                                       [<PERSON><PERSON>]     $3.39   \n", "2                                            [Apple]     $3.39   \n", "3                                                NaN     $2.39   \n", "4  [Tomatillo-<PERSON> (Hot), [Black Beans...    $16.98   \n", "5  [<PERSON> (Mild), [Rice, Cheese, Sou...    $10.98   \n", "6                                                NaN     $1.69   \n", "7  [Tomatillo Red Chili Salsa, [Fajita Vegetables...    $11.75   \n", "8  [Tomatillo Green <PERSON>, [<PERSON><PERSON>s, Ch...     $9.25   \n", "9  [<PERSON>, [<PERSON>, <PERSON>s, Pinto...     $9.25   "]}, "execution_count": 58, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.head(10)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 5. What is the number of observations in the dataset?"]}, {"cell_type": "code", "execution_count": 59, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["4622"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.shape[0]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 6. What is the number of columns in the dataset?"]}, {"cell_type": "code", "execution_count": 60, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["5"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.shape[1]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 7. Print the name of all the columns."]}, {"cell_type": "code", "execution_count": 61, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["Index(['order_id', 'quantity', 'item_name', 'choice_description',\n", "       'item_price'],\n", "      dtype='object')"]}, "execution_count": 61, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.columns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 8. How is the dataset indexed?"]}, {"cell_type": "code", "execution_count": 62, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["RangeIndex(start=0, stop=4622, step=1)"]}, "execution_count": 62, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.index"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 9. Which was the most-ordered item? "]}, {"cell_type": "code", "execution_count": 63, "metadata": {"collapsed": false, "tags": ["output.includes:Chicken Bowl"]}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>item_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Chicken Bowl</th>\n", "      <td>713926</td>\n", "      <td>761</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98 $10.98 $11.25 $8.75 $8.49 $11.25 $8.75 ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              order_id  quantity  \\\n", "item_name                          \n", "Chicken Bowl    713926       761   \n", "\n", "                                             choice_description  \\\n", "item_name                                                         \n", "Chicken Bowl  [Tomatillo-<PERSON> (Hot), [Black Beans...   \n", "\n", "                                                     item_price  \n", "item_name                                                        \n", "Chicken Bowl  $16.98 $10.98 $11.25 $8.75 $8.49 $11.25 $8.75 ...  "]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["c = chipo.groupby(\"item_name\")\n", "c = c.sum()\n", "c = c.sort_values([\"quantity\"], ascending=False)\n", "c.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 10. For the most-ordered item, how many items were ordered?"]}, {"cell_type": "code", "execution_count": 64, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>choice_description</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>item_name</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Chicken Bowl</th>\n", "      <td>713926</td>\n", "      <td>761</td>\n", "      <td>[Tomatillo<PERSON><PERSON> (Hot), [Black Beans...</td>\n", "      <td>$16.98 $10.98 $11.25 $8.75 $8.49 $11.25 $8.75 ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              order_id  quantity  \\\n", "item_name                          \n", "Chicken Bowl    713926       761   \n", "\n", "                                             choice_description  \\\n", "item_name                                                         \n", "Chicken Bowl  [Tomatillo-<PERSON> (Hot), [Black Beans...   \n", "\n", "                                                     item_price  \n", "item_name                                                        \n", "Chicken Bowl  $16.98 $10.98 $11.25 $8.75 $8.49 $11.25 $8.75 ...  "]}, "execution_count": 64, "metadata": {}, "output_type": "execute_result"}], "source": ["c = chipo.groupby(\"item_name\")\n", "c = c.sum()\n", "c = c.sort_values([\"quantity\"], ascending=False)\n", "c.head(1)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 11. What was the most ordered item in the choice_description column?"]}, {"cell_type": "code", "execution_count": 65, "metadata": {"collapsed": false, "tags": ["output.includes:Diet Coke"]}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>order_id</th>\n", "      <th>quantity</th>\n", "      <th>item_name</th>\n", "      <th>item_price</th>\n", "    </tr>\n", "    <tr>\n", "      <th>choice_description</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>[Diet Coke]</th>\n", "      <td>123455</td>\n", "      <td>159</td>\n", "      <td>Canned SodaCanned SodaCanned Soda6 Pack Soft D...</td>\n", "      <td>$2.18 $1.09 $1.09 $6.49 $2.18 $1.25 $1.09 $6.4...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                    order_id  quantity  \\\n", "choice_description                       \n", "[Diet Coke]           123455       159   \n", "\n", "                                                            item_name  \\\n", "choice_description                                                      \n", "[Diet Coke]         Canned SodaCanned SodaCanned Soda6 Pack Soft D...   \n", "\n", "                                                           item_price  \n", "choice_description                                                     \n", "[Diet Coke]         $2.18 $1.09 $1.09 $6.49 $2.18 $1.25 $1.09 $6.4...  "]}, "execution_count": 65, "metadata": {}, "output_type": "execute_result"}], "source": ["c = chipo.groupby(\"choice_description\").sum()\n", "c = c.sort_values([\"quantity\"], ascending=False)\n", "c.head(1)\n", "# Diet Coke 159"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 12. How many items were orderd in total?"]}, {"cell_type": "code", "execution_count": 66, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["4972"]}, "execution_count": 66, "metadata": {}, "output_type": "execute_result"}], "source": ["total_items_orders = chipo.quantity.sum()\n", "total_items_orders"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 13. Turn the item price into a float"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 13.a. Check the item price type"]}, {"cell_type": "code", "execution_count": 67, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["dtype('O')"]}, "execution_count": 67, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.item_price.dtype"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 13.b. Create a lambda function and change the type of item price, then print the item price type"]}, {"cell_type": "code", "execution_count": 68, "metadata": {"collapsed": true}, "outputs": [{"data": {"text/plain": ["dtype('float64')"]}, "execution_count": 68, "metadata": {}, "output_type": "execute_result"}], "source": ["dollarizer = lambda x: float(x[1:-1])\n", "chipo.item_price = chipo.item_price.apply(dollarizer)\n", "chipo.item_price.dtype"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Step 13.c. Check the item price type"]}, {"cell_type": "code", "execution_count": 69, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["dtype('float64')"]}, "execution_count": 69, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.item_price.dtype"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 14. How much was the revenue for the period in the dataset?"]}, {"cell_type": "code", "execution_count": 70, "metadata": {"collapsed": false}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Revenue was: $39237.02\n"]}], "source": ["revenue = (chipo[\"quantity\"] * chipo[\"item_price\"]).sum()\n", "\n", "print(\"Revenue was: $\" + str(np.round(revenue, 2)))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 15. How many orders were made in the period?"]}, {"cell_type": "code", "execution_count": 71, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["1834"]}, "execution_count": 71, "metadata": {}, "output_type": "execute_result"}], "source": ["orders = chipo.order_id.value_counts().count()\n", "orders"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 16. What is the average revenue amount per order?"]}, {"cell_type": "code", "execution_count": 72, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["21.39423118865867"]}, "execution_count": 72, "metadata": {}, "output_type": "execute_result"}], "source": ["average_revenue_per_order = revenue / orders\n", "average_revenue_per_order\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Step 17. How many different items are sold?"]}, {"cell_type": "code", "execution_count": 73, "metadata": {"collapsed": false}, "outputs": [{"data": {"text/plain": ["50"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["chipo.item_name.value_counts().count()"]}], "metadata": {"anaconda-cloud": {}, "kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 0, "copyrightYear": 2025, "license": "http://www.apache.org/licenses/LICENSE-2.0"}