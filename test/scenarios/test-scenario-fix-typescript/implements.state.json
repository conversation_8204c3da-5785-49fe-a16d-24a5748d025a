{"workspaceFoldersFilePaths": ["./"], "symbols": [], "activeFileDiagnostics": [{"start": {"line": 2, "character": 6}, "end": {"line": 2, "character": 7}, "message": "Class 'A' incorrectly implements interface 'B'.\n  Property 'name' is missing in type 'A' but required in type 'B'.", "severity": 0, "relatedInformation": [{"filePath": "./file2.ts", "start": {"line": 1, "character": 4}, "end": {"line": 1, "character": 8}, "message": "'name' is declared here."}]}, {"start": {"line": 2, "character": 6}, "end": {"line": 2, "character": 7}, "message": "'A' is declared but never used.", "severity": 3}], "activeTextEditor": {"selections": [{"anchor": {"line": 2, "character": 6}, "active": {"line": 2, "character": 7}, "isReversed": true}], "documentFilePath": "./file1.ts", "visibleRanges": [{"start": {"line": 0, "character": 0}, "end": {"line": 4, "character": 1}}], "languageId": "typescript"}, "debugConsoleOutput": "", "terminalBuffer": "", "terminalSelection": "", "notebookDocumentFilePaths": []}