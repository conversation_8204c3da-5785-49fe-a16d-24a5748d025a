{"activeTextEditor": {"selections": [{"anchor": {"line": 4, "character": 0}, "active": {"line": 10, "character": 1}, "start": {"line": 4, "character": 0}, "end": {"line": 10, "character": 1}}], "documentFilePath": "functions.ts", "visibleRanges": [{"start": {"line": 4, "character": 0}, "end": {"line": 10, "character": 1}}], "languageId": "typescript"}, "activeFileDiagnostics": [], "debugConsoleOutput": "Test", "terminalBuffer": "Test"}