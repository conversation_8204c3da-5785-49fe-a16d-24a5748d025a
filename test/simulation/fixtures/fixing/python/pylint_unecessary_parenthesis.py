# Define a list of participant
participant = ['<PERSON><PERSON><PERSON>', '<PERSON>',
               '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON>']

# Define the function to filters selected candidates
def selected_person(part):
    selected = ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>']
    if (part in selected):
        return True
    return False

selectedList = filter(selected_person, participant)

print('The selected candidates are:')
for candidate in selectedList:
    print(candidate)
