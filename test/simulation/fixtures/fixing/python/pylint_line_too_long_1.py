# ---------------------------------------------------------------------------------------------
#  Copyright (c) Microsoft Corporation and GitHub. All rights reserved.
# ---------------------------------------------------------------------------------------------
# pylint line-too-long: "error"
# pylint: disable=unused-argument, missing-module-docstring, missing-function-docstring, dangerous-default-value, too-many-arguments, trailing-whitespace


def complex_function(
    parameter1, parameter2, parameter3, parameter4, parameter5, parameter6, parameter7, parameter8, parameter9, parameter10, parameter11, parameter12, parameter13=13, long_assignment_variable=["This is a very cool string that is not an int or a boolean or a float or a complex number or anything of that sort but simple a fantastic string that exists as a string and identifies as a string."],):
    return 1
