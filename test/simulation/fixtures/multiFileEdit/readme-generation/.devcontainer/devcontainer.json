{"name": "Python 3-test", "image": "mcr.microsoft.com/devcontainers/python:1.1.9-3.11-bookworm", "postCreateCommand": "zsh -l .devcontainer/post-install.sh", "remoteUser": "root", "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-toolsai.jupyter", "ms-azuretools.vscode-azurefunctions", "mhutchie.git-graph", "charliermarsh.ruff", "ms-python.vscode-pylance", "esbenp.prettier-vscode"], "settings": {"python.pythonPath": "/usr/local/bin/python", "terminal.integrated.profiles.linux": {"zsh": {"path": "/usr/bin/zsh"}}, "terminal.integrated.defaultProfile.linux": "zsh"}}}}