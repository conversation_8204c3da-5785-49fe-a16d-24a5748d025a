{"name": "vscode-memfs", "displayName": "MemFS - a file system provider sample", "description": "Showcase for the file system provider API, also useful for testing again document that are not on disk.", "version": "0.0.3", "publisher": "vscode-samples", "private": true, "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Microsoft/vscode-extension-samples"}, "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onFileSystem:memfs"], "main": "./out/src/extension", "contributes": {"commands": [{"command": "memfs.workspaceInit", "title": "Setup Workspace", "category": "MemFS"}, {"command": "memfs.init", "title": "Create Files", "category": "MemFS"}, {"command": "memfs.reset", "title": "Delete Files", "category": "MemFS"}, {"command": "memfs.deleteFile", "title": "Delete \"file.txt\"", "category": "MemFS"}, {"command": "memfs.addFile", "title": "Add \"file.txt\"", "category": "MemFS"}], "menus": {"commandPalette": [{"command": "memfs.init", "when": "workbenchState == workspace"}, {"command": "memfs.reset", "when": "workbenchState == workspace"}, {"command": "memfs.deleteFile", "when": "workbenchState == workspace"}, {"command": "memfs.addFile", "when": "workbenchState == workspace"}, {"command": "memfs.workspaceInit", "when": "workbenchState != workspace"}]}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "lint": "eslint \"src/**/*.ts\"", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/node": "^18", "@types/vscode": "^1.73.0", "@typescript-eslint/eslint-plugin": "^6.7.0", "@typescript-eslint/parser": "^6.7.0", "eslint": "^8.26.0", "typescript": "^5.4.2"}}