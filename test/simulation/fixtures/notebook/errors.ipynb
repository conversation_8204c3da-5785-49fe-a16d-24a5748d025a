{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'pandas'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[1], line 1\u001b[0m\n\u001b[0;32m----> 1\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mpandas\u001b[39;00m \u001b[39mas\u001b[39;00m \u001b[39mpd\u001b[39;00m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'pandas'"]}], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 2\u001b[0m line \u001b[0;36m5\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m A \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([\u001b[39m1\u001b[39m, \u001b[39m0\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39m0\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39m0\u001b[39m], dtype\u001b[39m=\u001b[39m\u001b[39mbool\u001b[39m)\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m B \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([\u001b[39m1\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39m0\u001b[39m, \u001b[39m1\u001b[39m, \u001b[39m1\u001b[39m], dtype\u001b[39m=\u001b[39m\u001b[39mbool\u001b[39m)\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W1sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m A \u001b[39mor\u001b[39;00m B\n", "\u001b[0;31mValueError\u001b[0m: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()"]}], "source": ["import numpy as np\n", "\n", "A = np.array([1, 0, 1, 0, 1, 0], dtype=bool)\n", "B = np.array([1, 1, 1, 0, 1, 1], dtype=bool)\n", "A or B"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"ename": "InvalidArgumentError", "evalue": "{{function_node __wrapped__Reshape_device_/job:localhost/replica:0/task:0/device:CPU:0}} Input to reshape is a tensor with 3 values, but the requested shape has 2 [Op:Reshape]", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mInvalidArgumentError\u001b[0m                      Traceback (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 3\u001b[0m line \u001b[0;36m2\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W4sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mtensorflow\u001b[39;00m \u001b[39mas\u001b[39;00m \u001b[39mtf\u001b[39;00m\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W4sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m tf\u001b[39m.\u001b[39;49mreshape([\u001b[39m1\u001b[39;49m, \u001b[39m2\u001b[39;49m, \u001b[39m3\u001b[39;49m], (\u001b[39m2\u001b[39;49m,))\n", "File \u001b[0;32m/workspaces/vscode-copilot/.venv/lib/python3.9/site-packages/tensorflow/python/util/traceback_utils.py:153\u001b[0m, in \u001b[0;36mfilter_traceback.<locals>.error_handler\u001b[0;34m(*args, **kwargs)\u001b[0m\n\u001b[1;32m    151\u001b[0m \u001b[39mexcept\u001b[39;00m \u001b[39mException\u001b[39;00m \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m    152\u001b[0m   filtered_tb \u001b[39m=\u001b[39m _process_traceback_frames(e\u001b[39m.\u001b[39m__traceback__)\n\u001b[0;32m--> 153\u001b[0m   \u001b[39mraise\u001b[39;00m e\u001b[39m.\u001b[39mwith_traceback(filtered_tb) \u001b[39mfrom\u001b[39;00m \u001b[39mNone\u001b[39;00m\n\u001b[1;32m    154\u001b[0m \u001b[39mfinally\u001b[39;00m:\n\u001b[1;32m    155\u001b[0m   \u001b[39mdel\u001b[39;00m filtered_tb\n", "File \u001b[0;32m/workspaces/vscode-copilot/.venv/lib/python3.9/site-packages/tensorflow/python/eager/execute.py:53\u001b[0m, in \u001b[0;36mquick_execute\u001b[0;34m(op_name, num_outputs, inputs, attrs, ctx, name)\u001b[0m\n\u001b[1;32m     51\u001b[0m \u001b[39mtry\u001b[39;00m:\n\u001b[1;32m     52\u001b[0m   ctx\u001b[39m.\u001b[39mensure_initialized()\n\u001b[0;32m---> 53\u001b[0m   tensors \u001b[39m=\u001b[39m pywrap_tfe\u001b[39m.\u001b[39mTFE_Py_Execute(ctx\u001b[39m.\u001b[39m_handle, device_name, op_name,\n\u001b[1;32m     54\u001b[0m                                       inputs, attrs, num_outputs)\n\u001b[1;32m     55\u001b[0m \u001b[39mexcept\u001b[39;00m core\u001b[39m.\u001b[39m_NotOkStatusException \u001b[39mas\u001b[39;00m e:\n\u001b[1;32m     56\u001b[0m   \u001b[39mif\u001b[39;00m name \u001b[39mis\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mNone\u001b[39;00m:\n", "\u001b[0;31mInvalidArgumentError\u001b[0m: {{function_node __wrapped__Reshape_device_/job:localhost/replica:0/task:0/device:CPU:0}} Input to reshape is a tensor with 3 values, but the requested shape has 2 [Op:Reshape]"]}], "source": ["import tensorflow as tf\n", "tf.reshape([1, 2, 3], (2,))"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "This model has not yet been built. Build the model first by calling `build()` or by calling the model on a batch of data.", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[0m                                <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 4\u001b[0m line \u001b[0;36m5\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W3sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mtensorflow\u001b[39;00m \u001b[39mas\u001b[39;00m \u001b[39mtf\u001b[39;00m\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W3sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m model \u001b[39m=\u001b[39m tf\u001b[39m.\u001b[39mkeras\u001b[39m.\u001b[39mSequential([\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W3sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m   tf\u001b[39m.\u001b[39mkeras\u001b[39m.\u001b[39mlayers\u001b[39m.\u001b[39mDense(\u001b[39m1\u001b[39m)\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W3sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m ])\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W3sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m model\u001b[39m.\u001b[39;49msummary()\n", "File \u001b[0;32m/workspaces/vscode-copilot/.venv/lib/python3.9/site-packages/keras/src/engine/training.py:3403\u001b[0m, in \u001b[0;36mModel.summary\u001b[0;34m(self, line_length, positions, print_fn, expand_nested, show_trainable, layer_range)\u001b[0m\n\u001b[1;32m   3372\u001b[0m \u001b[39m\u001b[39m\u001b[39m\"\"\"Prints a string summary of the network.\u001b[39;00m\n\u001b[1;32m   3373\u001b[0m \n\u001b[1;32m   3374\u001b[0m \u001b[39mArgs:\u001b[39;00m\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   3400\u001b[0m \u001b[39m    ValueError: if `summary()` is called before the model is built.\u001b[39;00m\n\u001b[1;32m   3401\u001b[0m \u001b[39m\"\"\"\u001b[39;00m\n\u001b[1;32m   3402\u001b[0m \u001b[39mif\u001b[39;00m \u001b[39mnot\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mbuilt:\n\u001b[0;32m-> 3403\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mValueError\u001b[39;00m(\n\u001b[1;32m   3404\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mThis model has not yet been built. \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   3405\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mBuild the model first by calling `build()` or by calling \u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   3406\u001b[0m         \u001b[39m\"\u001b[39m\u001b[39mthe model on a batch of data.\u001b[39m\u001b[39m\"\u001b[39m\n\u001b[1;32m   3407\u001b[0m     )\n\u001b[1;32m   3408\u001b[0m layer_utils\u001b[39m.\u001b[39mprint_summary(\n\u001b[1;32m   3409\u001b[0m     \u001b[39mself\u001b[39m,\n\u001b[1;32m   3410\u001b[0m     line_length\u001b[39m=\u001b[39mline_length,\n\u001b[0;32m   (...)\u001b[0m\n\u001b[1;32m   3415\u001b[0m     layer_range\u001b[39m=\u001b[39mlayer_range,\n\u001b[1;32m   3416\u001b[0m )\n", "\u001b[0;31mValueError\u001b[0m: This model has not yet been built. Build the model first by calling `build()` or by calling the model on a batch of data."]}], "source": ["import tensorflow as tf\n", "model = tf.keras.Sequential([\n", "  tf.keras.layers.Dense(1)\n", "])\n", "model.build(input_shape=(None, 1))\n", "model.summary()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "unsupported operand type(s) for +: 'int' and 'NoneType'", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 5\u001b[0m line \u001b[0;36m3\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W4sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mnumpy\u001b[39;00m \u001b[39mas\u001b[39;00m \u001b[39mnp\u001b[39;00m\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W4sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m vals1 \u001b[39m=\u001b[39m np\u001b[39m.\u001b[39marray([\u001b[39m1\u001b[39m, \u001b[39mNone\u001b[39;00m, \u001b[39m3\u001b[39m, \u001b[39m4\u001b[39m])\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W4sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m vals1\u001b[39m.\u001b[39;49msum()\n", "File \u001b[0;32m/workspaces/vscode-copilot/.venv/lib/python3.9/site-packages/numpy/core/_methods.py:49\u001b[0m, in \u001b[0;36m_sum\u001b[0;34m(a, axis, dtype, out, keepdims, initial, where)\u001b[0m\n\u001b[1;32m     47\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m_sum\u001b[39m(a, axis\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, dtype\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, out\u001b[39m=\u001b[39m\u001b[39mNone\u001b[39;00m, keepdims\u001b[39m=\u001b[39m\u001b[39mFalse\u001b[39;00m,\n\u001b[1;32m     48\u001b[0m          initial\u001b[39m=\u001b[39m_NoValue, where\u001b[39m=\u001b[39m\u001b[39mTrue\u001b[39;00m):\n\u001b[0;32m---> 49\u001b[0m     \u001b[39mreturn\u001b[39;00m umr_sum(a, axis, dtype, out, keepdims, initial, where)\n", "\u001b[0;31mTypeError\u001b[0m: unsupported operand type(s) for +: 'int' and 'NoneType'"]}], "source": ["import numpy as np\n", "\n", "vals1 = np.array([1, None, 3, 4])\n", "vals1.sum()"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["1 [ a_var outside a_func() ]\n"]}, {"ename": "UnboundLocalError", "evalue": "local variable 'a_var' referenced before assignment", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mUnboundLocalError\u001b[0m                         <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 6\u001b[0m line \u001b[0;36m9\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m     \u001b[39mreturn\u001b[39;00m a_var\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=7'>8</a>\u001b[0m \u001b[39mprint\u001b[39m(a_var, \u001b[39m'\u001b[39m\u001b[39m[ a_var outside a_func() ]\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m a_func()\n", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 6\u001b[0m line \u001b[0;36m4\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39ma_func\u001b[39m():\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m     a_var \u001b[39m=\u001b[39m a_var \u001b[39m+\u001b[39m \u001b[39m1\u001b[39m\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m     \u001b[39mprint\u001b[39m(a_var, \u001b[39m'\u001b[39m\u001b[39m[ a_var inside a_func() ]\u001b[39m\u001b[39m'\u001b[39m)\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#W5sdnNjb2RlLXJlbW90ZQ%3D%3D?line=5'>6</a>\u001b[0m     \u001b[39mreturn\u001b[39;00m a_var\n", "\u001b[0;31mUnboundLocalError\u001b[0m: local variable 'a_var' referenced before assignment"]}], "source": ["a_var = 1\n", "\n", "def a_func():\n", "    a_var = a_var + 1\n", "    print(a_var, '[ a_var inside a_func() ]')\n", "    return a_var\n", "\n", "print(a_var, '[ a_var outside a_func() ]')\n", "a_func()\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'int' object is not callable", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 7\u001b[0m line \u001b[0;36m4\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m kid_ages \u001b[39m=\u001b[39m [\u001b[39m2\u001b[39m, \u001b[39m7\u001b[39m, \u001b[39m5\u001b[39m, \u001b[39m6\u001b[39m, \u001b[39m3\u001b[39m]\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m \u001b[39mmax\u001b[39m \u001b[39m=\u001b[39m \u001b[39m0\u001b[39m\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m max_value \u001b[39m=\u001b[39m \u001b[39mmax\u001b[39;49m(kid_ages)\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X12sdnNjb2RlLXJlbW90ZQ%3D%3D?line=4'>5</a>\u001b[0m \u001b[39mprint\u001b[39m(max_value)\n", "\u001b[0;31mTypeError\u001b[0m: 'int' object is not callable"]}], "source": ["kid_ages = [2, 7, 5, 6, 3]\n", "\n", "max = 0\n", "max_value = max_val\n", "print(max_value)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "can't set attribute", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mAttributeError\u001b[0m                            <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 8\u001b[0m line \u001b[0;36m1\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=6'>7</a>\u001b[0m         \u001b[39mreturn\u001b[39;00m \u001b[39mself\u001b[39m\u001b[39m.\u001b[39mdata\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=8'>9</a>\u001b[0m foo \u001b[39m=\u001b[39m Foo(\u001b[39m23\u001b[39m)\n\u001b[0;32m---> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X13sdnNjb2RlLXJlbW90ZQ%3D%3D?line=9'>10</a>\u001b[0m foo\u001b[39m.\u001b[39;49mx \u001b[39m=\u001b[39m \u001b[39m42\u001b[39m\n", "\u001b[0;31mAttributeError\u001b[0m: can't set attribute"]}], "source": ["class Foo(object):\n", "    def __init__(self, data):\n", "        self.data = data\n", "\n", "    @property\n", "    def x(self):\n", "        return self.data\n", "\n", "foo = Foo(23)\n", "foo.x = 42"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "Index does not support mutable operations", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 9\u001b[0m line \u001b[0;36m4\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X14sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39mimport\u001b[39;00m \u001b[39mpandas\u001b[39;00m \u001b[39mas\u001b[39;00m \u001b[39mpd\u001b[39;00m\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X14sdnNjb2RlLXJlbW90ZQ%3D%3D?line=2'>3</a>\u001b[0m ind \u001b[39m=\u001b[39m pd\u001b[39m.\u001b[39mIndex([\u001b[39m2\u001b[39m, \u001b[39m3\u001b[39m, \u001b[39m5\u001b[39m, \u001b[39m7\u001b[39m, \u001b[39m11\u001b[39m])\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X14sdnNjb2RlLXJlbW90ZQ%3D%3D?line=3'>4</a>\u001b[0m ind[\u001b[39m1\u001b[39;49m] \u001b[39m=\u001b[39m \u001b[39m0\u001b[39m\n", "File \u001b[0;32m/workspaces/vscode-copilot/.venv/lib/python3.9/site-packages/pandas/core/indexes/base.py:5347\u001b[0m, in \u001b[0;36mIndex.__setitem__\u001b[0;34m(self, key, value)\u001b[0m\n\u001b[1;32m   5345\u001b[0m \u001b[39m@final\u001b[39m\n\u001b[1;32m   5346\u001b[0m \u001b[39mdef\u001b[39;00m \u001b[39m__setitem__\u001b[39m(\u001b[39mself\u001b[39m, key, value) \u001b[39m-\u001b[39m\u001b[39m>\u001b[39m \u001b[39mNone\u001b[39;00m:\n\u001b[0;32m-> 5347\u001b[0m     \u001b[39mraise\u001b[39;00m \u001b[39mTypeError\u001b[39;00m(\u001b[39m\"\u001b[39m\u001b[39mIndex does not support mutable operations\u001b[39m\u001b[39m\"\u001b[39m)\n", "\u001b[0;31mTypeError\u001b[0m: Index does not support mutable operations"]}], "source": ["import pandas as pd\n", "\n", "ind = pd.Index([2, 3, 5, 7, 11])\n", "ind[1] = 0"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "'str' object is not an iterator", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 10\u001b[0m line \u001b[0;36m2\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m g \u001b[39m=\u001b[39m \u001b[39m'\u001b[39m\u001b[39mabc\u001b[39m\u001b[39m'\u001b[39m\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X16sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m \u001b[39mnext\u001b[39;49m(g)\n", "\u001b[0;31mTypeError\u001b[0m: 'str' object is not an iterator"]}], "source": ["g = 'abc'\n", "next(g)"]}, {"cell_type": "code", "execution_count": 21, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "can only concatenate str (not \"int\") to str", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 11\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X21sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m \u001b[39m'\u001b[39;49m\u001b[39m3.14\u001b[39;49m\u001b[39m'\u001b[39;49m \u001b[39m+\u001b[39;49m \u001b[39m3\u001b[39;49m\n", "\u001b[0;31mTypeError\u001b[0m: can only concatenate str (not \"int\") to str"]}], "source": ["'3.14' + 3"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"ename": "NameError", "evalue": "name 'array' is not defined", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mNameError\u001b[0m                                 <PERSON><PERSON> (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 12\u001b[0m line \u001b[0;36m1\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X22sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m array([\u001b[39m1\u001b[39m, \u001b[39m2\u001b[39m, \u001b[39m3\u001b[39m, \u001b[39m4\u001b[39m, \u001b[39m5\u001b[39m])\n", "\u001b[0;31mNameError\u001b[0m: name 'array' is not defined"]}], "source": ["array([1, 2, 3, 4, 5])"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"ename": "TypeError", "evalue": "can only concatenate list (not \"str\") to list", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mTypeError\u001b[0m                                 Traceback (most recent call last)", "\u001b[1;32m/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb Cell 13\u001b[0m line \u001b[0;36m2\n\u001b[1;32m      <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X23sdnNjb2RlLXJlbW90ZQ%3D%3D?line=0'>1</a>\u001b[0m foo \u001b[39m=\u001b[39m [\u001b[39m'\u001b[39m\u001b[39mhello\u001b[39m\u001b[39m'\u001b[39m, \u001b[39m'\u001b[39m\u001b[39mworld\u001b[39m\u001b[39m'\u001b[39m]\n\u001b[0;32m----> <a href='vscode-notebook-cell://codespaces%2Bsuper-duper-engine-q7qwgj4x7xh9v/workspaces/vscode-copilot/test/simulation/fixtures/notebook/errors.ipynb#X23sdnNjb2RlLXJlbW90ZQ%3D%3D?line=1'>2</a>\u001b[0m foo\u001b[39m+\u001b[39;49m\u001b[39m'\u001b[39;49m\u001b[39mbar\u001b[39;49m\u001b[39m'\u001b[39;49m\n", "\u001b[0;31mTypeError\u001b[0m: can only concatenate list (not \"str\") to list"]}], "source": ["foo = ['hello', 'world']\n", "foo+'bar'"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.2"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}