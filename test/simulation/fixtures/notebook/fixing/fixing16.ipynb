{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from abc import ABC\n", "from dataclasses import dataclass\n", "from typing import Generic, TypeVar\n", "\n", "T = TypeVar('T')\n", "\n", "@dataclass\n", "class Msg(Generic[T]):\n", "    data: T\n", "\n", "class Foo:\n", "    foo: bool\n", "\n", "class Bar:\n", "    bar: float\n", "\n", "class BaseHandler(Generic[T], ABC):\n", "    def handle(self, msg: Msg[T]):\n", "        pass\n", "\n", "FooBar = Foo | Bar\n", "\n", "class FooBarHandler(BaseHandler[FooBar]):\n", "    def handle(self, msg: Msg[FooBar]):\n", "        pass\n", "\n", "foobar_handler = FooBarHandler()\n", "\n", "msg = Msg(data=Foo())\n", "foobar_handler.handle(msg)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}