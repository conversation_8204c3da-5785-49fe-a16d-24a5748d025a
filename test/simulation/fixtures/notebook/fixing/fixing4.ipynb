{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This same tests the type checker's ability to validate\n", "# types related to coroutines (and async/await) statements.\n", "\n", "from typing import Generator, Any, Optional\n", "\n", "\n", "async def coroutine1():\n", "    return 1\n", "\n", "\n", "a = coroutine1()\n", "\n", "# This should generate an error because 'await'\n", "# can't be used outside of an async function.\n", "await a\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}