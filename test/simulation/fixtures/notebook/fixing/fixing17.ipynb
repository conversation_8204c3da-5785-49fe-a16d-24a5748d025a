{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import random\n", "import typing\n", "\n", "optional_str: typing.Optional[str] = None\n", "\n", "if random.random() > 0.5:\n", "    optional_str = \"This is your chance\"\n", "\n", "def check_optional_str() -> bool:\n", "    return optional_str is not None\n", "\n", "if check_optional_str():\n", "    print(optional_str.upper())\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}