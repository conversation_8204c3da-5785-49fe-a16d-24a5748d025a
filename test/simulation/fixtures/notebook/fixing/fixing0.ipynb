{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from abc import ABC, abstractmethod\n", "from typing import Type\n", "\n", "class Base(ABC):\n", "    @abstractmethod\n", "    def foo(self, x: int) -> int:\n", "        pass\n", "\n", "def foo2():\n", "    Base()"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}