{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# This sample uses a non-breaking space, which should generate errors in\n", "# the tokenizer, parser and type checker.\n", "\n", "# The space between \"import\" and \"sys\" is a non-breaking UTF8 character.\n", "import sys\n", "\n"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}