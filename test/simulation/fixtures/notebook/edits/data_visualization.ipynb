{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Data Visualization Notebook\n", "\n", "This notebook demonstrates how to visualize and analyze sales data using reusable functions."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Data Loading"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load sample sales data\n", "data = pd.DataFrame({\n", "    'month': ['Jan', 'Feb', 'Mar', 'Apr', 'May'],\n", "    'sales': [200, 220, 250, 270, 300],\n", "    'region': ['North', 'North', 'East', 'East', 'West']\n", "})\n", "data.head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualization"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Reusable function for plotting\n", "def plot_sales(df, region=None):\n", "    \"\"\"\n", "    Plot sales data.\n", "\n", "    Args:\n", "        df (pd.DataFrame): The input DataFrame containing sales data.\n", "        region (str, optional): The region to filter data. Defaults to None.\n", "\n", "    Returns:\n", "        None\n", "    \"\"\"\n", "    if region:\n", "        df = df[df['region'] == region]\n", "\n", "    plt.figure(figsize=(8, 5))\n", "    sns.lineplot(data=df, x='month', y='sales', marker='o')\n", "    plt.xlabel('Month')\n", "    plt.ylabel('Sales')\n", "    plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot sales for the North region"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_sales(data, region='North')"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Plot sales for all regions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["plot_sales(data)"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}