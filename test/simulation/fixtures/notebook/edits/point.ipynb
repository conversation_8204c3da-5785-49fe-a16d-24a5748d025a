{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["class Point2D:\n", "    '''\n", "    A 2D point in Cartesian coordinates.\n", "    '''\n", "\n", "    def __init__(self, xCoord: float, yCoord: float) -> None:\n", "        self.xCoord = xCoord\n", "        self.yCoord = yCoord\n", "\n", "    def __str__(self) -> str:\n", "        '''\n", "        Converts the Point2D object to a string.\n", "        '''\n", "        return f'({self.xCoord}, {self.yCoord})'\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def distance_from_origin(point: Point2D) -> str:\n", "    '''\n", "    Calculates the distance of a Point2D object from the origin.\n", "    '''\n", "    return (point.xCoord**2 + point.yCoord**2)**0.5"]}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 2}