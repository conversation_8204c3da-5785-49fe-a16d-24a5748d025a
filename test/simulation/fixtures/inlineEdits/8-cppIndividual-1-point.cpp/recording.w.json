{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "1a97d256-dbe3-47ab-8447-7c938e2ce8d0", "repoRootUri": "file:///c%3a/users/brmurtau/onedrive%20-%20microsoft/documents/github/edit-projects", "opStart": 90, "opEndEx": 96}}, {"kind": "documentEncountered", "id": 0, "time": 1730827649445, "relativePath": "8-cppIndividual\\1-point.cpp"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730827649445, "content": "#include <cmath>\r\n\r\nclass Point {\r\npublic:\r\n    Point(double x, double y) : x(x), y(y) {}\r\n    double getDistance() const;\r\n\r\nprivate:\r\n    double x;\r\n    double y;\r\n};\r\n\r\ndouble Point::getDistance() const {\r\n    return sqrt(x * x + y * y);\r\n}"}, {"kind": "changed", "id": 0, "time": 1730827649439, "edit": [[31, 31, "3D"]]}], "nextUserEdit": {"edit": [[74, 74, ", double z"], [88, 88, ", z(z)"], [166, 166, "\r\n    double z;"], [181, 186, "Point3D"], [240, 240, " + z * z"]], "relativePath": "8-cppIndividual\\1-point.cpp", "originalOpIdx": 214}}