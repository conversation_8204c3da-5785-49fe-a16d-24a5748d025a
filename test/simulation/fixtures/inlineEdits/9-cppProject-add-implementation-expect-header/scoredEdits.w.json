{"$web-editor.format-json": true, "$web-editor.default-url": "https://microsoft.github.io/vscode-workbench-recorder-viewer/?editRating", "edits": [{"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[282, 282, "() const;\n    std::optional<double> getAverage"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[52, 52, "#include <numeric>\n#include <cmath>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[52, 52, "#include <cmath>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[54, 107, " class Statistics {\npublic:\n    void add(double value);\n    std::optional<double> getAverage() const"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[54, 282, "\tclass Statistics {\n\tpublic:\n\t\tvoid add(double value);\n\t\tstd::optional<double> getMean() const;\n\t\tstd::optional<double> getStandardDeviation() const;\n\t\tstd::optional<double> getMin() const;\n\t\tstd::optional<double> getMax() const;\n\t\tstd::optional<double> getAverage"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[54, 291, " class Statistics {\npublic:\n    void add(double value);\n    std::optional<double> getMean() const; // mean\n    std::optional<double> getStandardDeviation() const; // standard deviation\n    std::optional<double> getMin() const; // minimum\n    std::optional<double> getMax() const; // maximum\n    std::optional<double> getAverage() const; // average"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": null, "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[51, 52, ""]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[52, 52, "#include <algorithm>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[52, 52, "#include <numeric> // Added for accumulate function"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[52, 52, "#include <numeric>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[52, 52, "#include <algorithm>\n#include <numeric>\n#include <cmath>"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[292, 301, "    std::optional<double> getAverage() const;\n"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[292, 292, "\n    std::optional<double> getAverage() const;"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[302, 302, "  "]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[279, 282, "Average"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[292, 302, "    std::optional<double> getAverage() const;\n\nprivate:\n  "]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[279, 302, "Average() const;\n\nprivate:\n  "]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[291, 291, "\n    std::optional<double> getAverage() const; // Added method declaration"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[291, 291, "\n    std::optional<double> getAverage() const; // New method declaration"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[291, 291, "\n    std::optional<double> getAverage() const; // Added method declaration for getAverage"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[73, 282, "  public:\n    void add(double value);\n    std::optional<double> getMean() const;\n    std::optional<double> getStandardDeviation() const;\n    std::optional<double> getMin() const;\n    std::optional<double> getMax() const;\n    std::optional<double> getAverage"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[292, 334, "    std::optional<double> getAverage() const"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///d%3a/dev/microsoft/edit-projects/9-cppProject/simple-multifile/stats.h", "edit": [[292, 292, "    std::optional<double> getAverage() const;"]], "scoreCategory": "nextEdit", "score": 0}], "scoringContext": {"kind": "recording", "recording": {"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "dba1d6c8-b2b3-4345-9a25-03270b53e682", "repoRootUri": "file:///d%3a/dev/microsoft/edit-projects", "opStart": 958, "opEndEx": 1015}}, {"kind": "documentEncountered", "id": 0, "time": 1730979523846, "relativePath": "9-cppProject\\simple-multifile\\stats.cpp"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730979523846, "content": "#include \"stats.h\"\n\n#include <cmath>\n#include <vector>\n\n\nvoid Statistics::add(double value)\n{\n    samples.push_back(value);\n}\n\nstd::optional<double> Statistics::getMean() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double sum = 0;\n    for (double sample : samples)\n        sum += sample;\n    return sum / samples.size();\n}\n\nstd::optional<double> Statistics::getStandardDeviation() const\n{\n    std::optional<double> mean = getMean();\n    if (!mean)\n        return std::nullopt;\n\n    double sum = 0;\n    for (double sample : samples)\n    {\n        sum += (sample - *mean) * (sample - *mean);\n    }\n    return std::sqrt(sum / samples.size() - 1);\n}\n\nstd::optional<double> Statistics::getMin() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double min = samples[0];\n    for (double sample : samples)\n    {\n        if (sample < min)\n        {\n            min = sample;\n        }\n    }\n    return min;\n}\n\nstd::optional<double> Statistics::getMax() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double max = samples[0];\n    for (double sample : samples)\n    {\n        if (sample > max)\n        {\n            max = sample;\n        }\n    }\n    return max;\n}"}, {"kind": "changed", "id": 0, "time": 1730979467623, "edit": [[1212, 1212, "\n"]]}, {"kind": "changed", "id": 0, "time": 1730979481552, "edit": [[1213, 1213, "\nstd::optional<double> Statistics::getMax() const { return std::nullopt; }"]]}, {"kind": "changed", "id": 0, "time": 1730979483621, "edit": [[1287, 1287, "\n"]]}, {"kind": "changed", "id": 0, "time": 1730979516627, "edit": [[1251, 1254, "Average"]]}, {"kind": "documentEncountered", "id": 1, "time": 1730979523846, "relativePath": "9-cppProject\\simple-multifile\\stats.h"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "time": 1730979523846, "content": "#pragma once\n\n#include <optional>\n#include <vector>\n\nclass Statistics {\npublic:\n    void add(double value);\n    std::optional<double> getMean() const;\n    std::optional<double> getStandardDeviation() const;\n    std::optional<double> getMin() const;\n    std::optional<double> getMax() const;\n\nprivate:\n  std::vector<double> samples;\n};"}, {"kind": "changed", "id": 1, "time": 1730979523846, "edit": [[52, 52, "\n"]]}], "nextUserEdit": {"edit": [[292, 292, "    std::optional<double> getAverage() const;\n"]], "relativePath": "9-cppProject\\simple-multifile\\stats.h", "originalOpIdx": 1019}}}}