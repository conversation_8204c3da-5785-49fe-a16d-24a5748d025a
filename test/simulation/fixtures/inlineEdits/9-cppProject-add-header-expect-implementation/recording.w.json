{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "dba1d6c8-b2b3-4345-9a25-03270b53e682", "repoRootUri": "file:///d%3a/dev/microsoft/edit-projects", "opStart": 915, "opEndEx": 945}}, {"kind": "documentEncountered", "id": 1, "time": 1730979445332, "relativePath": "9-cppProject\\simple-multifile\\stats.h"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 1, "time": 1730979445332, "content": "#pragma once\n\n#include <optional>\n#include <vector>\n\nclass Statistics {\npublic:\n    void add(double value);\n    std::optional<double> getMean() const;\n    std::optional<double> getStandardDeviation() const;\n    std::optional<double> getMin() const;\n\nprivate:\n  std::vector<double> samples;\n};"}, {"kind": "changed", "id": 1, "time": 1730979314551, "edit": [[248, 248, "\n    std::optional<double> getMax() const;"]]}, {"kind": "documentEncountered", "id": 2, "time": 1730979445332, "relativePath": "9-cppProject\\simple-multifile\\stats.cpp"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 2, "time": 1730979445332, "content": "#include \"stats.h\"\n\n#include <cmath>\n#include <vector>\n\nvoid Statistics::add(double value)\n{\n    samples.push_back(value);\n}\n\nstd::optional<double> Statistics::getMean() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double sum = 0;\n    for (double sample : samples)\n        sum += sample;\n    return sum / samples.size();\n}\n\nstd::optional<double> Statistics::getStandardDeviation() const\n{\n    std::optional<double> mean = getMean();\n    if (!mean)\n        return std::nullopt;\n\n    double sum = 0;\n    for (double sample : samples)\n    {\n        sum += (sample - *mean) * (sample - *mean);\n    }\n    return std::sqrt(sum / samples.size() - 1);\n}\n\nstd::optional<double> Statistics::getMin() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double min = samples[0];\n    for (double sample : samples)\n    {\n        if (sample < min)\n        {\n            min = sample;\n        }\n    }\n    return min;\n}\n"}, {"kind": "changed", "id": 2, "time": 1730979337053, "edit": [[940, 940, "\n"]]}], "nextUserEdit": {"edit": [[941, 941, "\nstd::optional<double> Statistics::getMax() const\n{\n    if (samples.empty())\n        return std::nullopt;\n\n    double max = samples[0];\n    for (double sample : samples)\n    {\n        if (sample > max)\n        {\n            max = sample;\n        }\n    }\n    return max;\n}"]], "relativePath": "9-cppProject\\simple-multifile\\stats.cpp", "originalOpIdx": 957}}