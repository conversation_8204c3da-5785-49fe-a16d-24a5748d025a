{"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "dba1d6c8-b2b3-4345-9a25-03270b53e682", "repoRootUri": "file:///d%3a/dev/microsoft/edit-projects", "opStart": 45, "opEndEx": 191}}, {"kind": "documentEncountered", "id": 0, "time": 1730976719610, "relativePath": "6-vscode-remote-try-java\\src\\main\\java\\com\\mycompany\\app\\App.java"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730976719610, "content": "/*----------------------------------------------------------------------------------------\n * Copyright (c) Microsoft Corporation. All rights reserved.\n * Licensed under the MIT License. See LICEN<PERSON> in the project root for license information.\n *---------------------------------------------------------------------------------------*/\n\npackage com.mycompany.app;\n\nimport java.util.Scanner;\n\npublic class App {\n    public static void main(String[] args) {\n        Scanner scan = new Scanner(System.in);\n\n        System.out.println(\"Hello Remote World!\");\n\n        // Ask for the user's name\n        System.out.print(\"Enter your name: \");\n        String name = scan.nextLine();\n\n        // Greet the user\n        System.out.println(\"Hello, \" + name + \"! Welcome to the Remote World!\");\n\n        // Ask for the user's age\n        System.out.print(\"Enter your age: \");\n        int age = scan.nextInt();\n        scan.nextLine(); // Consume newline\n\n        if (age < 18) {\n            System.out.println(\"You're quite young, \" + name + \"!\");\n        } else {\n            System.out.println(\"Nice to meet you, \" + name + \"!\");\n        }\n\n        // Ask for the user's favorite programming language\n        System.out.print(\"Enter your favorite programming language: \");\n        String language = scan.nextLine();\n\n        System.out.println(language + \" is a great choice!\");\n\n        // Simple calculator\n        System.out.println(\"Let's do some basic arithmetic.\");\n        System.out.print(\"Enter the first number: \");\n        double num1 = scan.nextDouble();\n\n        System.out.print(\"Enter the second number: \");\n        double num2 = scan.nextDouble();\n\n        System.out.println(\"Choose an operation (+, -, *, /): \");\n        char operation = scan.next().charAt(0);\n\n        double result = 0;\n        switch (operation) {\n            case '+':\n                result = num1 + num2;\n                break;\n            case '-':\n                result = num1 - num2;\n                break;\n            case '*':\n                result = num1 * num2;\n                break;\n            case '/':\n                if (num2 != 0) {\n                    result = num1 / num2;\n                } else {\n                    System.out.println(\"Error: Division by zero is not allowed.\");\n                    scan.close();\n                    return;\n                }\n                break;\n            default:\n                System.out.println(\"Invalid operation.\");\n                scan.close();\n                return;\n        }\n\n        System.out.println(\"The result of the operation is: \" + result);\n\n        scan.close();\n    }\n}"}, {"kind": "changed", "id": 0, "time": 1730976719608, "edit": [[816, 819, "height, rounded to the nearest decimal or two"]]}], "nextUserEdit": {"edit": [[899, 902, "height"], [916, 985, "double height = scan.nextDouble();\n        scan.nextLine(); // Consume newline"], [1005, 1007, "140"], [1056, 1061, "small"]], "relativePath": "6-vscode-remote-try-java\\src\\main\\java\\com\\mycompany\\app\\App.java", "originalOpIdx": 247}}