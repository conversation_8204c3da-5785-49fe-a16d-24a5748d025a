{"$web-editor.format-json": true, "$web-editor.default-url": "https://microsoft.github.io/vscode-workbench-recorder-viewer/?editRating", "edits": [{"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[785, 785, "\nfunction identityNumber(arg: number): number {\n    return arg;\n}"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[0, 38, ""]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[727, 762, " identity<T>(arg: T): T"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[787, 787, "\nfunction identityNumber(arg: number): number {\n    return arg;\n}\n"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": null, "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[762, 762, " {\n    return arg;\n}\n\nfunction identityNumber(arg: number): number"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1292, "String"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[785, 785, "\n\nfunction identity<T>(arg: T): T {\n    return arg;\n}"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[785, 785, "\n\n// Function with Generics\nfunction identityNumber(arg: number): number {\n    return arg;\n}"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[785, 785, "\n\nfunction identityNumber(arg: number): number {\n    return arg;\n}"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[716, 787, "\nfunction identity<T>(arg: T): T {\n    return arg;\n}\n"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1148, 1292, "\nconsole.log(format(\"  Hello World  \")); // Output: Hello World\nconsole.log(identityString"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1101, 1330, "// Function with Optional Parameters\nfunction farewell(name: string, farewell?: string): string {\n    return `${farewell || 'Goodbye'}, ${name}!`;\n}\n\n// Function with Default Parameters\nfunction multiply(a: number, b: number, c: number): number {\n    return a * b * c;\n}\n\n// Arrow Function\nconst add = (a: number, b: number): number => a + b;\n\n// Function with Rest Parameters\nfunction sum(...numbers: number[]): number {\n    return numbers.reduce((acc, curr) => acc + curr, 0);\n}\n\n// Function with Union Types\nfunction format(input: string | number): string {"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[684, 687, ""]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1292, "String(\"42\")); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1284, "String<number>(42)); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1292, "String(42)); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[617, 1292, "\n        return input.toFixed(2);\n    }\n    return input.trim();\n}\n\n// Function with Generics\nfunction identityString(arg: string): string {\n    return arg;\n}\n\n// Example usage\nconsole.log(farewell(\"Alice\")); // Output: Hello, <PERSON>!\nconsole.log(farewell(\"<PERSON>\", \"<PERSON>\")); // Output: Hi, <PERSON>!\nconsole.log(multiply(5, 1, 3)); // Output: 15\nconsole.log(multiply(5, 2, 4)); // Output: 40\nconsole.log(add(3, 4)); // Output: 7\nconsole.log(sum(1, 2, 3, 4, 5)); // Output: 15\nconsole.log(format(123.456)); // Output: 123.46\nconsole.log(format(\"  Hello World  \")); // Output: Hello World\nconsole.log(identityString(42)); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[617, 1292, "\n        return input.toFixed(2);\n    }\n    return input.trim();\n}\n\n// Function with Generics\nfunction identityString(arg: string): string {\n    return arg;\n}\n\n// Example usage\nconsole.log(farewell(\"Alice\")); // Output: Hello, <PERSON>!\nconsole.log(farewell(\"<PERSON>\", \"<PERSON>\")); // Output: Hi, <PERSON>!\nconsole.log(multiply(5, 1, 3)); // Output: 15\nconsole.log(multiply(5, 2, 4)); // Output: 40\nconsole.log(add(3, 4)); // Output: 7\nconsole.log(sum(1, 2, 3, 4, 5)); // Output: 15\nconsole.log(format(123.456)); // Output: 123.46\nconsole.log(format(\"  Hello World  \")); // Output: Hello World\nconsole.log(identityString(\"42\")); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[617, 1284, "\n        return input.toFixed(2);\n    }\n    return input.trim();\n}\n\n// Function with Generics\nfunction identityString(arg: string): string {\n    return arg;\n}\n\n// Example usage\nconsole.log(farewell(\"Alice\")); // Output: Hello, <PERSON>!\nconsole.log(farewell(\"<PERSON>\", \"<PERSON>\")); // Output: Hi, <PERSON>!\nconsole.log(multiply(5, 1, 3)); // Output: 15\nconsole.log(multiply(5, 2, 4)); // Output: 40\nconsole.log(add(3, 4)); // Output: 7\nconsole.log(sum(1, 2, 3, 4, 5)); // Output: 15\nconsole.log(format(123.456)); // Output: 123.46\nconsole.log(format(\"  Hello World  \")); // Output: Hello World\nconsole.log(identityString<number>(42)); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1292, "String(\"Hello, World!\")); // Output: Hello, World!\nconsole.log(identityString"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1308, "String(\"TypeScript\")); // Output: TypeScript"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1308, "String(\"Hello, TypeScript!\")); // Output: Hello, TypeScript!"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1292, "String(\"Hello\")); // Output: Hello\nconsole.log(identityString"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// console.log(identity<number>(42)); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1330, "String(\"Hello\")); // Output: Hello"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// Updated to use the new identityString function\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// Updated identity function to accept only string\nconsole.log(identityString(\"Hello\")); // Output: Hello\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[0, 0, "// Function with Optional Parameters\r\nfunction farewell(name: string, farewell?: string): string {\r\n    return `${farewell || 'Goodbye'}, ${name}!`;\r\n}\r\n\r\n// Function with Default Parameters\r\nfunction multiply(a: number, b: number, c: number): number {\r\n    return a * b * c;\r\n}\r\n\r\n// Arrow Function\r\nconst add = (a: number, b: number): number => a + b;\r\n\r\n// Function with Rest Parameters\r\nfunction sum(...numbers: number[]): number {\r\n    return numbers.reduce((acc, curr) => acc + curr, 0);\r\n}\r\n\r\n// Function with Union Types\r\nfunction format(input: string | number): string {\r\n    if (typeof input === 'number') {\r\n        return input.toFixed(2);\r\n    }\r\n    return input.trim();\r\n}\r\n\r\n// Function with Generics\r\nfunction identityString(arg: string): string {\r\n    return arg;\r\n}\r\n\r\n// Example usage\r\nconsole.log(farewell(\"Alice\")); // Output: Hello, Alice!\r\nconsole.log(farewell(\"Bob\", \"Hi\")); // Output: Hi, Bob!\r\nconsole.log(multiply(5, 1, 3)); // Output: 15\r\nconsole.log(multiply(5, 2, 4)); // Output: 40\r\nconsole.log(add(3, 4)); // Output: 7\r\nconsole.log(sum(1, 2, 3, 4, 5)); // Output: 15\r\nconsole.log(format(123.456)); // Output: 123.46\r\nconsole.log(format(\"  Hello World  \")); // Output: Hello World\r\n"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1330, "String(\"TypeScript\")); // Output: TypeScript\n"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1330, "String(\"Hello\")); // Output: Hello\nconsole.log(identityString(\"TypeScript\")); // Output: TypeScript\n"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1330, "String(\"Hello\")); // Output: Hello\n"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// Updated identity function usage\nconsole.log(identityString(\"Hello\")); // Output: Hello\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1292, "String(\"Hello, Generics!\")); // Output: Hello, Generics!\nconsole.log(identityString"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// console.log(identity<number>(42)); // Output: 42\nconsole.log(identityString(\"Hello\")); // Output: Hello\nconsole.log(identityString"]], "scoreCategory": "valid", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1330, "// Updated identity function to accept only string type\nconsole.log(identityString(\"Hello\")); // Output: Hello\n// Removed the previous identity function call"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// Updated identity function usage\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// Updated identity function to accept only string type\nconsole.log(identityString(\"Hello\")); // Output: Hello\n// Removed the previous identity function call for number type\n// console.log(identity<number>(42)); // Output: 42\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1330, "String(\"Hello, TypeScript!\")); // Output: Hello, TypeScript!\n"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1330, "String(\"Hello, TypeScript!\")); // Output: Hello, TypeScript!"]], "scoreCategory": "nextEdit", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1292, "// console.log(identity<number>(42)); // Output: 42\n// console.log(identity<string>(\"TypeScript\")); // Output: TypeScript\nconsole.log(identityString"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1214, 1330, "// console.log(identity<number>(42)); // Output: 42\n// console.log(identity<string>(\"TypeScript\")); // Output: TypeScript\nconsole.log(identityString(\"Hello\")); // Output: Hello"]], "scoreCategory": "bad", "score": 0}, {"documentUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects/1-individualFiles/2-collection.ts", "edit": [[1234, 1330, "String(\"Hello, World!\")); // Output: Hello, World!"]], "score": 0, "scoreCategory": "bad"}], "scoringContext": {"kind": "recording", "recording": {"log": [{"kind": "meta", "data": {"kind": "log-origin", "uuid": "ac936431-6c08-4c3c-8497-bc9b71c522cc", "repoRootUri": "file:///c%3a/users/brmurtau/downloads/github/edit-projects", "opStart": 1261, "opEndEx": 1338}}, {"kind": "documentEncountered", "id": 0, "time": 1730931029134, "relativePath": "1-individualFiles\\2-collection.ts"}, {"kind": "<PERSON><PERSON><PERSON><PERSON>", "id": 0, "time": 1730931029134, "content": "// Function with Optional Parameters\r\nfunction farewell(name: string, farewell?: string): string {\r\n    return `${farewell || 'Goodbye'}, ${name}!`;\r\n}\r\n\r\n// Function with Default Parameters\r\nfunction multiply(a: number, b: number, c: number): number {\r\n    return a * b * c;\r\n}\r\n\r\n// Arrow Function\r\nconst add = (a: number, b: number): number => a + b;\r\n\r\n// Function with Rest Parameters\r\nfunction sum(...numbers: number[]): number {\r\n    return numbers.reduce((acc, curr) => acc + curr, 0);\r\n}\r\n\r\n// Function with Union Types\r\nfunction format(input: string | number): string {\r\n    if (typeof input === 'number') {\r\n        return input.toFixed(2);\r\n    }\r\n    return input.trim();\r\n}\r\n\r\n// Function with Generics\r\nfunction identity<T>(arg: T): T {\r\n    return arg;\r\n}\r\n\r\n// Example usage\r\nconsole.log(farewell(\"Alice\")); // Output: Hello, Alice!\r\nconsole.log(farewell(\"Bob\", \"Hi\")); // Output: Hi, Bob!\r\nconsole.log(multiply(5, 1, 3)); // Output: 15\r\nconsole.log(multiply(5, 2, 4)); // Output: 40\r\nconsole.log(add(3, 4)); // Output: 7\r\nconsole.log(sum(1, 2, 3, 4, 5)); // Output: 15\r\nconsole.log(format(123.456)); // Output: 123.46\r\nconsole.log(format(\"  Hello World  \")); // Output: Hello World\r\nconsole.log(identity<number>(42)); // Output: 42\r\nconsole.log(identity<string>(\"TypeScript\")); // Output: TypeScript"}, {"kind": "changed", "id": 0, "time": 1730931017090, "edit": [[735, 735, "String"]]}, {"kind": "changed", "id": 0, "time": 1730931020153, "edit": [[741, 744, ""]]}, {"kind": "changed", "id": 0, "time": 1730931024272, "edit": [[747, 748, "string"]]}, {"kind": "changed", "id": 0, "time": 1730931026814, "edit": [[756, 757, "string"]]}], "nextUserEdit": {"edit": [[1234, 1242, "String"], [1243, 1245, "\"hi\""], [1260, 1262, "hi"], [1284, 1292, "String"]], "relativePath": "1-individualFiles\\2-collection.ts", "originalOpIdx": 1452}}}}