{"name": "@vscode/prompt-tsx", "version": "0.2.7-alpha", "description": "Declare LLM prompts with TSX", "main": "./dist/base/index.js", "types": "./dist/base/index.d.ts", "scripts": {"build": "node build/esbuild.js", "compile": "tsc -p tsconfig.json && tsx ./build/postcompile.ts", "watch": "tsc --watch --sourceMap", "test": "vscode-test", "prettier": "prettier --list-different --write --cache .", "prepare": "tsx ./build/postinstall.ts"}, "keywords": [], "author": "Microsoft Corporation", "license": "SEE LICENSE IN LICENSE", "bugs": {"url": "https://github.com/Microsoft/vscode/issues"}, "devDependencies": {"@microsoft/tiktokenizer": "^1.0.6", "@types/node": "^20.11.30", "@vscode/test-cli": "^0.0.9", "@vscode/test-electron": "^2.4.1", "@types/vscode": "^1.89.0", "esbuild": "0.25.0", "mocha": "^10.2.0", "npm-dts": "^1.3.12", "prettier": "^2.8.7", "tsx": "^4.6.2", "typescript": "^5.4.2"}}