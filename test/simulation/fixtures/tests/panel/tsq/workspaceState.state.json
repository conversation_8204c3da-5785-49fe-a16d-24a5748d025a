{"workspaceFoldersFilePaths": ["./"], "symbols": [], "activeFileDiagnostics": [], "debugConsoleOutput": "", "terminalBuffer": "", "terminalSelection": "", "terminalShellType": "bash", "repoContexts": [{"rootUri": {"$mid": 1, "path": "/Users/<USER>/code/tsq", "scheme": "file"}, "headBranchName": "main", "headCommitHash": "5654bc1157c85a74388c80cc17268067a9d35d73", "upstreamBranchName": "main", "upstreamRemote": "origin", "isRebasing": true, "remotes": ["origin"], "remoteFetchUrls": ["**************:jrieken/vscode-tree-sitter-query.git"]}], "notebookDocumentFilePaths": []}