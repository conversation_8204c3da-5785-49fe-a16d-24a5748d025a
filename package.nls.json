{"github.copilot.badge.signUp": "Sign up for GitHub Copilot", "github.copilot.badge.star": "Star Copilot on GitHub", "github.copilot.badge.youtube": "Check out GitHub on Youtube", "github.copilot.badge.twitter": "Follow GitHub on Twitter", "github.copilot.icon": "GitHub Copilot icon", "github.copilot.command.explainThis": "Explain", "github.copilot.command.reviewAndComment": "Review and Comment", "github.copilot.command.applyReviewSuggestion": "Apply", "github.copilot.command.applyReviewSuggestionAndNext": "Apply and Go to Next", "github.copilot.command.discardReviewSuggestion": "Discard", "github.copilot.command.discardReviewSuggestionAndNext": "Discard and Go to Next", "github.copilot.command.discardAllReviewSuggestion": "Discard All", "github.copilot.command.reviewStagedChanges": "Code Review - Staged Changes", "github.copilot.command.reviewUnstagedChanges": "Code Review - Unstaged Changes", "github.copilot.command.reviewChanges": "Code Review - Uncommitted Changes", "github.copilot.command.reviewChanges.cancel": "Code Review - Cancel", "github.copilot.command.gotoPreviousReviewSuggestion": "Previous Suggestion", "github.copilot.command.gotoNextReviewSuggestion": "Next Suggestion", "github.copilot.command.continueReviewInInlineChat": "<PERSON>ard and <PERSON><PERSON> to Inline Chat", "github.copilot.command.continueReviewInChat": "View in Chat Panel", "github.copilot.command.helpfulReviewSuggestion": "Helpful", "github.copilot.command.unhelpfulReviewSuggestion": "Unhelpful", "github.copilot.command.fixThis": "Fix", "github.copilot.command.generateThis": "Generate This", "github.copilot.command.generateDocs": "Generate Docs", "github.copilot.command.generateTests": "Generate Tests", "github.copilot.command.openUserPreferences": "Open User Preferences", "github.copilot.command.sendChatFeedback": "<PERSON> <PERSON><PERSON>", "github.copilot.command.buildLocalWorkspaceIndex": "Build Local Workspace Index", "github.copilot.command.buildRemoteWorkspaceIndex": "Build Remote Workspace Index", "github.copilot.viewsWelcome.signIn": {"message": "Sign in to enable features powered by Github Copilot.\n\n[Sign in](command:workbench.action.chat.triggerSetupForceSignIn)", "comment": ["{Locked='['}", "{Locked='](command:workbench.action.chat.triggerSetupForceSignIn)'}"]}, "github.copilot.viewsWelcome.individual.expired": "Your Copilot subscription has expired.\n\n[Review Copilot Settings](https://github.com/settings/copilot?editor=vscode)", "github.copilot.viewsWelcome.enterprise": "Contact your GitHub organization administrator to enable Copilot.", "github.copilot.viewsWelcome.offline": {"message": "You are currently offline. Please connect to the internet to use GitHub Copilot.\n\n[Retry Connection](command:github.copilot.refreshToken)", "comment": ["{Locked='['}", "{Locked='](command:github.copilot.refreshToken)'}"]}, "github.copilot.viewsWelcome.contactSupport": {"message": "There seems to be a problem with your account. Please contact GitHub support.\n\n[Contact Support](https://support.github.com/?editor=vscode)", "comment": ["{Locked='['}", "{Locked='](https://support.github.com/?editor=vscode)'}"]}, "github.copilot.viewsWelcome.chatDisabled": {"message": "GitHub Copilot <PERSON> is currently disabled for your account by an organization administrator. Contact an organization administrator to enable chat.\n\n[Learn More](https://docs.github.com/en/copilot/managing-copilot/managing-github-copilot-in-your-organization/managing-github-copilot-features-in-your-organization/managing-policies-for-copilot-in-your-organization)", "comment": ["{Locked='['}", "{Locked='](https://docs.github.com/en/copilot/managing-copilot/managing-github-copilot-in-your-organization/managing-github-copilot-features-in-your-organization/managing-policies-for-copilot-in-your-organization)'}"]}, "github.copilot.viewsWelcome.switchToReleaseChannel": {"message": "The Pre-Release version of the GitHub Copilot Chat extension is not currently supported in the stable version of VS Code. Please switch to the release version for GitHub Copilot Chat or try VS Code Insiders.\n\n[Switch to Release Version and Reload](command:runCommands?%7B%22commands%22%3A%5B%7B%22command%22%3A%22workbench.extensions.action.switchToRelease%22%2C%22args%22%3A%5B%22GitHub.copilot-chat%22%5D%7D%2C%22workbench.action.reloadWindow%22%5D%7D)\n\n[Switch to VS Code Insiders](https://aka.ms/vscode-insiders)", "comment": ["{Locked='['}", "{Locked='](command:runCommands?%7B%22commands%22%3A%5B%7B%22command%22%3A%22workbench.extensions.action.switchToRelease%22%2C%22args%22%3A%5B%22GitHub.copilot-chat%22%5D%7D%2C%22workbench.action.reloadWindow%22%5D%7D)'}", "{Locked='](https://aka.ms/vscode-insiders)'}"]}, "github.copilot.viewsWelcome.debug": {"message": "Debug using a [terminal command](command:github.copilot.chat.startCopilotDebugCommand) or in an [interactive chat](command:workbench.action.chat.open?%7B%22query%22%3A%22%40vscode%20%2FstartDebugging%20%22%2C%22isPartialQuery%22%3Atrue%7D).", "comment": ["{Locked='['}", "{Locked='](command:github.copilot.chat.startCopilotDebugCommand)'}", "{Locked='](command:workbench.action.chat.open?%7B%22query%22%3A%22%40vscode%20%2FstartDebugging%20%22%2C%22isPartialQuery%22%3Atrue%7D)'}"]}, "github.copilot.command.logWorkbenchState": "Log Workbench State", "github.copilot.command.showChatLogView": "Show Chat Debug View", "github.copilot.command.showContextInspectorView": "Inspect Language Context", "github.copilot.command.resetVirtualToolGroups": "Reset Virtual Tool Groups", "github.copilot.command.applySuggestionWithCopilot": "Apply Suggestion", "github.copilot.command.explainTerminalSelection": "Explain Terminal Selection", "github.copilot.command.explainTerminalSelectionContextMenu": "Explain", "github.copilot.command.explainTerminalLastCommand": "Explain Last Terminal Command", "github.copilot.command.attachTerminalSelection": "Add Terminal Selection to Chat", "github.copilot.command.collectWorkspaceIndexDiagnostics": "Collect Workspace Index Diagnostics", "github.copilot.git.generateCommitMessage": "Generate Commit Message", "github.copilot.devcontainer.generateDevContainerConfig": "Generate Dev Container Configuration", "github.copilot.config.enableCodeActions": "Controls if Copilot commands are shown as Code Actions when available", "github.copilot.config.renameSuggestions.triggerAutomatically": "Controls whether Copilot generates suggestions for renaming", "github.copilot.config.localeOverride": "Specify a locale that <PERSON>pi<PERSON> should respond in, e.g. `en` or `fr`. By default, Copilot will respond using VS Code's configured display language locale.", "github.copilot.config.edits.enabled": "Whether to enable the Copilot Edits feature.", "github.copilot.config.edits.codesearch.enabled": "This setting is deprecated in favor of `#github.copilot.chat.codesearch.enabled#`.", "github.copilot.config.codesearch.enabled": "Whether to enable agentic codesearch when using `#codebase`.", "github.copilot.nextEditSuggestions.enabled": "Whether to enable next edit suggestions (NES).\n\nNES can propose a next edit based on your recent changes. [Learn more](https://aka.ms/vscode-nes) about next edit suggestions.", "github.copilot.nextEditSuggestions.fixes": "Whether to offer fixes for diagnostics via next edit suggestions (NES).", "github.copilot.chat.copilotDebugCommand.enabled": "Whether the `copilot-debug` command is enabled in the terminal.", "github.copilot.config.terminalChatLocation": "Controls where chat queries from the terminal should be opened.", "github.copilot.config.terminalChatLocation.chatView": "Open the chat view.", "github.copilot.config.terminalChatLocation.quickChat": "Open quick chat.", "github.copilot.config.terminalChatLocation.terminal": "Open terminal inline chat", "github.copilot.config.scopeSelection": "Whether to prompt the user to select a specific symbol scope if the user uses `/explain` and the active editor has no selection.", "github.copilot.config.debugTerminalCommands": "Whether to quick fix hints in the debug terminal and the `copilot-debug` command.", "github.copilot.config.debugTerminalCommandPatterns": "A list of commands for which the \"Debug Command\" quick fix action should be shown in the debug terminal.", "github.copilot.config.edits.suggestRelatedFilesFromGitHistory": "Whether to suggest related files from git history for the Copilot Edits working set.", "github.copilot.chat.edits.suggestRelatedFilesForTests": "Whether to suggest source files from test files for the Copilot Edits working set.", "github.copilot.config.codeGeneration.instructions": "A set of instructions that will be added to Copilot requests that generate code.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use underscore for field names.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "github.copilot.config.codeGeneration.instructions.deprecated": "Use instructions files instead. See https://aka.ms/vscode-ghcp-custom-instructions for more information.", "github.copilot.config.codeGeneration.useInstructionFiles": "Controls whether code instructions from `.github/copilot-instructions.md` are added to Copilot requests.\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance. [Learn more](https://aka.ms/github-copilot-custom-instructions) about customizing Copilot.", "github.copilot.config.codeGeneration.instruction.text": "A text instruction that will be added to Copilot requests that generate code. Optionally, you can specify a language for the instruction.", "github.copilot.config.codeGeneration.instruction.file": "A path to a file that will be added to Copilot requests that generate code. Optionally, you can specify a language for the instruction.", "github.copilot.config.testGeneration.instructions": "A set of instructions that will be added to Copilot requests that generate tests.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use underscore for field names.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "github.copilot.config.testGeneration.instructions.deprecated": "Use instructions files instead. See https://aka.ms/vscode-ghcp-custom-instructions for more information.", "github.copilot.config.experimental.testGeneration.instruction.text": "A text instruction that will be added to Copilot requests that generate tests. Optionally, you can specify a language for the instruction.", "github.copilot.config.experimental.testGeneration.instruction.file": "A path to a file that will be added to Copilot requests that generate tests. Optionally, you can specify a language for the instruction.", "github.copilot.config.reviewSelection.enabled": "Enables code review on current selection.", "github.copilot.config.reviewSelection.instructions": "A set of instructions that will be added to Copilot requests that provide code review for the current selection.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use underscore for field names.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's effectiveness.", "github.copilot.config.reviewSelection.instruction.text": "A text instruction that will be added to Copilot requests that provide code review for the current selection. Optionally, you can specify a language for the instruction.", "github.copilot.config.reviewSelection.instruction.file": "A path to a file that will be added to Copilot requests that provide code review for the current selection. Optionally, you can specify a language for the instruction.", "github.copilot.config.commitMessageGeneration.instructions": "A set of instructions that will be added to Copilot requests that generate commit messages.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Use conventional commit message format.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "github.copilot.config.commitMessageGeneration.instruction.text": "Text instructions that will be added to Copilot requests that generate commit messages.", "github.copilot.config.commitMessageGeneration.instruction.file": "A path to a file with instructions that will be added to Copilot requests that generate commit messages.", "github.copilot.config.pullRequestDescriptionGeneration.instructions": "A set of instructions that will be added to Copilot requests that generate pull request titles and descriptions.\nInstructions can come from: \n- a file in the workspace: `{ \"file\": \"fileName\" }`\n- text in natural language: `{ \"text\": \"Always include a list of key changes.\" }`\n\nNote: Keep your instructions short and precise. Poor instructions can degrade Copilot's quality and performance.", "github.copilot.config.pullRequestDescriptionGeneration.instruction.text": "Text instructions that will be added to Copilot requests that generate pull request titles and descriptions.", "github.copilot.config.pullRequestDescriptionGeneration.instruction.file": "A path to a file with instructions that will be added to Copilot requests that generate pull request titles and descriptions.", "github.copilot.config.generateTests.codeLens": "Show 'Generate tests' code lens for symbols that are not covered by current test coverage information.", "github.copilot.config.notebook.followCellExecution": "Controls whether the currently executing cell is revealed into the viewport upon execution from Copilot.", "github.copilot.chat.editor.temporalContext.enabled": "When making inline chat request whether to include recently viewed and edited files with Copilot requests.", "github.copilot.chat.edits.temporalContext.enabled": "When making edits request whether to include recently viewed and edited files with Copilot requests.", "github.copilot.config.startDebugging.enabled": "Enables the `/startDebugging` intent in panel chat. Generates or finds launch config to match the query (if any), project structure, and more.", "github.copilot.config.agent.runTasks": "Configures whether Copilot Edits can run workspace tasks in agent mode.", "github.copilot.config.agent.thinkingTool": "Enables the thinking tool that allows <PERSON><PERSON><PERSON> to think deeply about your request before generating a response in agent mode.", "github.copilot.config.setupTests.enabled": "Enables the `/setupTests` intent and prompting in `/tests` generation.", "github.copilot.config.byok.ollamaEndpoint": "The endpoint to use for the Ollama when accessed via bring your own key. Defaults to localhost.", "github.copilot.config.virtualTools.threshold": "This setting defines the tool count over which virtual tools should be used. Virtual tools group similar sets of tools together and they allow the model to activate them on-demand. Certain tool groups will optimistically be pre-activated. We are actively developing this feature and you experience degraded tool calling once the threshold is hit.\n\nMay be set to `0` to disable virtual tools.", "github.copilot.config.retryAfterFilteredResponse.enabled": "Enables retrying after a filtered response. If enabled, <PERSON><PERSON><PERSON><PERSON> will retry the request after a content filter blocks the response.", "github.copilot.command.fixTestFailure": "Fix Test Failure", "copilot.description": "Ask or edit in context", "copilot.edits.description": "Edit files in your workspace", "copilot.agent.description": "Edit files in your workspace in agent mode", "copilot.workspace.description": "Ask about your workspace", "copilot.workspace.sampleRequest": "How do I build this project?", "copilot.workspace.explain.description": "Explain how the code in your active editor works", "copilot.workspace.edit.description": "Edit files in your workspace", "copilot.workspace.review.description": "Review the selected code in your active editor", "copilot.workspace.edit.inline.description": "Edit the selected code in your active editor", "copilot.workspace.generate.description": "Generate new code", "copilot.workspace.doc.description": "Add documentation comment for this symbol", "copilot.workspace.tests.description": "Generate unit tests for the selected code", "copilot.workspace.fix.description": "Propose a fix for the problems in the selected code", "copilot.workspace.fix.sampleRequest": "There is a problem in this code. Rewrite the code to show it with the bug fixed.", "copilot.workspace.new.description": "Scaffold code for a new file or project in a workspace", "copilot.workspace.new.sampleRequest": "Create a RESTful API server using typescript", "copilot.workspace.newNotebook.description": "Create a new Jupyter Notebook", "copilot.workspace.newNotebook.sampleRequest": "How do I create a notebook to load data from a csv file?", "copilot.workspace.semanticSearch.description": "Find relevant code to your query", "copilot.workspace.semanticSearch.sampleRequest": "Where is the toolbar code?", "copilot.vscode.description": "Ask questions about VS Code", "copilot.workspaceSymbols.tool.description": "Search for workspace symbols using language services.", "copilot.listCodeUsages.tool.description": "Find references, definitions, and other usages of a symbol", "copilot.codebase.tool.description": "Find relevant file chunks, symbols, and other information in your codebase", "copilot.vscode.tool.description": "Use VS Code API references to answer questions about VS Code extension development.", "copilot.testFailure.tool.description": "Includes information about the last unit test failure", "copilot.vscode.sampleRequest": "What is the command to open the integrated terminal?", "copilot.vscode.api.description": "Ask about VS Code extension development", "copilot.vscode.api.sampleRequest": "How do I add text to the status bar?", "copilot.vscode.search.description": "Generate query parameters for workspace search", "copilot.vscode.search.sampleRequest": "Search for 'foo' in all files under my 'src' directory", "copilot.vscode.startDebugging.description": "Generate launch config and start debugging in VS Code (Experimental)", "copilot.vscode.startDebugging.sampleRequest": "Attach to node app at port 9229", "copilot.vscode.setupTests.description": "Set up tests in your project (Experimental)", "copilot.vscode.setupTests.sampleRequest": "add playwright tests to my project", "copilot.terminal.description": "Ask about commands", "copilot.terminalPanel.description": "Ask how to do something in the terminal", "copilot.terminal.sampleRequest": "How do I view all files within a directory including sub-directories?", "copilot.terminal.explain.description": "Explain something in the terminal", "copilot.terminal.explain.sampleRequest": "Explain the last command", "github.copilot.submenu.copilot.label": "Copilot", "github.copilot.submenu.reviewComment.applyAndNext.label": "Apply and Go to Next", "github.copilot.submenu.reviewComment.discardAndNext.label": "Discard and Go to Next", "github.copilot.submenu.reviewComment.discard.label": "Discard", "github.copilot.config.useProjectTemplates": "Use relevant GitHub projects as starter projects when using `/new`", "github.copilot.chat.attachSelection": "Add Selection to Chat", "github.copilot.command.collectDiagnostics": "GitHub Copilot Chat Diagnostics", "github.copilot.command.inlineEdit.clearCache": "GitHub Copilot Chat Clear Next Edit Cache", "github.copilot.command.showNotebookLog": "Show Chat Log Notebook", "github.copilot.resetAutomaticCommandExecutionPrompt": "Reset Automatic Command Execution Prompt", "github.copilot.command.generateSTest": "Generate STest From Last Chat Request", "github.copilot.command.generateConfiguration": "Generate Debug Configuration with GitHub Copilot", "github.copilot.command.openWalkthrough": "Open Walkthrough", "github.copilot.walkthrough.title": "GitHub Copilot", "github.copilot.walkthrough.description": "Your AI pair programmer to write code faster and smarter", "github.copilot.walkthrough.signIn.title": "Sign in with GitHub", "github.copilot.walkthrough.signIn.description": "To get started with Copilot, sign in with your GitHub account.\nMake sure you're using the correct GitHub account. You can also sign in later using the account menu.\n\n[Sign In](command:github.copilot.signIn)", "github.copilot.walkthrough.signIn.media.altText": "Sign in to GitHub via this walkthrough or VS Code's account menu", "github.copilot.walkthrough.setup.signIn.title": "Sign in to use Copilot for free", "github.copilot.walkthrough.setup.signIn.description": "You can use Copilot to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.\n We now offer [Copilot for free](https://github.com/features/copilot/plans) with your GitHub account.\n\n[Use Copilot for Free](command:workbench.action.chat.triggerSetupForceSignIn)", "github.copilot.walkthrough.setup.signUp.title": "Get started with Copilot for free", "github.copilot.walkthrough.setup.signUp.description": "You can use Copilot to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.\n We now offer [Copilot for free](https://github.com/features/copilot/plans) with your GitHub account.\n\n[Use Copilot for Free](command:workbench.action.chat.triggerSetupForceSignIn)", "github.copilot.walkthrough.setup.noAction.description": "You can use Copilot to generate code across multiple files, fix errors, ask questions about your code and much more using natural language.\n We now offer [Copilot for free](https://github.com/features/copilot/plans) with your GitHub account.", "github.copilot.walkthrough.firstSuggest.title": "AI-suggested code completions", "github.copilot.walkthrough.firstSuggest.description": "As you type in the editor, <PERSON><PERSON><PERSON> suggests code to help you complete what you started.", "github.copilot.walkthrough.firstSuggest.media.altText": "The video shows different Copilot completions, where <PERSON><PERSON><PERSON> suggests code to help the user complete their code", "github.copilot.walkthrough.panelChat.title": "Chat about your code", "github.copilot.walkthrough.panelChat.description": "Ask Copilot programming questions or get help with your code using **@workspace**.\n Type **@** to see all available chat participants that you can chat with directly, each with their own expertise.\n[Chat with Copilot](command:workbench.action.chat.open?%7B%22mode%22%3A%22ask%22%7D)", "github.copilot.walkthrough.panelChat.media.altText": "The user invokes @workspace in the Copilot Chat panel in the secondary sidebar to understand the code base. Copilot retrieves the relevant information and provides a response with links to the files", "github.copilot.walkthrough.inlineChatNotMac.title": "Use natural language in your files", "github.copilot.walkthrough.inlineChatNotMac.description": "Sometimes, it's easier to describe the code you want to write directly within a file.\nPlace your cursor or make a selection and use **``Ctrl+I``** to open **Inline Chat**.", "github.copilot.walkthrough.inlineChatNotMac.media.altText": "Inline Chat view in the editor. The video shows the user invoking the inline chat widget and asking <PERSON><PERSON><PERSON> to make a change in the file using natural language. <PERSON><PERSON><PERSON> then makes the requested change", "github.copilot.walkthrough.inlineChatMac.title": "Use natural language in your files", "github.copilot.walkthrough.inlineChatMac.description": "Sometimes, it's easier to describe the code you want to write directly within a file.\nPlace your cursor or make a selection and use **``Cmd+I``** to open **Inline Chat**.", "github.copilot.walkthrough.inlineChatMac.media.altText": "The video shows the user invoking the inline chat widget and asking <PERSON><PERSON><PERSON> to make a change in the file using natural language. <PERSON><PERSON><PERSON> then makes the requested change", "github.copilot.walkthrough.edits.title": "Make changes using natural language", "github.copilot.walkthrough.edits.description": "Use **Copilot Edits** to select files you want to work with and describe changes you want to make. Copilot applies them directly to your files.\n[Edit with Copilot](command:workbench.action.chat.open?%7B%22mode%22%3A%22edit%22%7D)", "github.copilot.walkthrough.edits.media.altText": "The video shows the user dragging and dropping files into the Copilot Edits input box located in the secondary sidebar. Copilot then updates the file according to the user’s request", "github.copilot.walkthrough.sparkle.title": "Look out for smart actions", "github.copilot.walkthrough.sparkle.description": "Copilot enhances your coding experience with AI-powered smart actions throughout the VS Code interface.\nLook for $(sparkle) icons, such as in the [Source Control view](command:workbench.view.scm), where <PERSON><PERSON><PERSON> generates commit messages and PR descriptions based on code changes.\n\n[Discover Tips and Tricks](https://code.visualstudio.com/docs/copilot/copilot-vscode-features)", "github.copilot.walkthrough.sparkle.media.altText": "The video shows the sparkle icon in the source control input box being clicked, triggering GitHub Copilot to generate a commit message automatically", "github.copilot.chat.completionContext.typescript.mode": "The execution mode of the TypeScript Copilot context provider.", "github.copilot.chat.languageContext.typescript.enabled": "Enables the TypeScript language context provider for inline completions", "github.copilot.chat.languageContext.typescript.cacheTimeout": "The cache population timeout for the TypeScript language context provider in milliseconds. The default is 500 milliseconds.", "github.copilot.chat.languageContext.fix.typescript.enabled": "Enables the TypeScript language context provider for /fix commands", "github.copilot.chat.languageContext.inline.typescript.enabled": "Enables the TypeScript language context provider for inline chats (both generate and edit)", "github.copilot.command.rerunWithCopilotDebug": "Debug Last Terminal Command", "github.copilot.config.enableUserPreferences": "Enable remembering user preferences in agent mode.", "github.copilot.tools.terminalSelection.name": "Terminal Selection", "github.copilot.tools.terminalSelection.description": "The active terminal's selection", "github.copilot.tools.terminalLastCommand.name": "Terminal Last Command", "github.copilot.tools.terminalLastCommand.description": "The active terminal's last run command", "github.copilot.tools.createAndRunTask.name": "Create and Run Task", "github.copilot.tools.createAndRunTask.description": "Create and run a task in the workspace", "github.copilot.tools.createAndRunTask.userDescription": "Create and run a task in the workspace", "github.copilot.config.newWorkspaceCreation.enabled": "Whether to enable new agentic workspace creation.", "github.copilot.config.newWorkspace.useContext7": "Whether to use the [Context7](command:github.copilot.mcp.viewContext7) tools to scaffold project for new workspace creation.", "github.copilot.config.editsNewNotebook.enabled": "Whether to enable the new notebook tool in Copilot Edits.", "github.copilot.config.notebook.inlineEditAgent.enabled": "Enable agent-like behavior from the notebook inline chat widget.", "github.copilot.config.summarizeAgentConversationHistory.enabled": "Whether to auto-summarize agent conversation history once the context window is filled.", "github.copilot.tools.createNewWorkspace.name": "Create New Workspace", "github.copilot.tools.openEmptyFolder.name": "Open an empty folder as VS Code workspace", "github.copilot.tools.getProjectSetupInfo.name": "Get Project Setup Info", "github.copilot.tools.searchResults.name": "Search View Results", "github.copilot.tools.searchResults.description": "The results from the search view", "github.copilot.tools.githubRepo.name": "Search GitHub Repository", "github.copilot.tools.githubRepo.userDescription": "Searches a GitHub repository for relevant source code snippets. You can specify a repository using `owner/repo`", "github.copilot.config.autoFix": "Automatically fix diagnostics for edited files.", "github.copilot.chat.virtualTools.enabled": "Automatically group large number of language model tools to improve tool calling and avoid model limitations.", "github.copilot.tools.createNewWorkspace.userDescription": "Scaffold a new workspace in VS Code", "copilot.tools.errors.description": "Check errors for a particular file", "copilot.tools.applyPatch.description": "Edit text files in the workspace", "copilot.tools.openSimpleBrowser.description": "Preview a locally hosted website in the Simple Browser", "copilot.tools.findTestFiles.description": "For a source code file, find the file that contains the tests. For a test file, find the file that contains the code under test", "copilot.tools.getDocInfo.description": "For a symbol like a class or function, find the information about how to document it", "copilot.tools.changes.description": "Get diffs of changed files", "copilot.tools.newJupyterNotebook.description": "Create a new Jupyter Notebook", "copilot.tools.runNotebookCell.description": "Trigger the execution of a cell in a notebook file", "copilot.tools.getNotebookCellOutput.description": "Read the output of a previously executed cell", "copilot.tools.fetchWebPage.description": "Fetch the main content from a web page. You should include the URL of the page you want to fetch.", "copilot.tools.searchCodebase.name": "Codebase", "copilot.tools.searchWorkspaceSymbols.name": "Workspace Symbols", "copilot.tools.listCodeUsages.name": "Find Usages", "copilot.tools.getVSCodeAPI.name": "Get VS Code API References", "copilot.tools.think.name": "Think", "copilot.tools.findFiles.name": "Find Files", "copilot.tools.findTextInFiles.name": "Find Text In Files", "copilot.tools.applyPatch.name": "Apply Patch", "copilot.tools.readFile.name": "Read File", "copilot.tools.listDirectory.name": "List Dir", "copilot.tools.getTaskOutput.name": "Get Task Output", "copilot.tools.getErrors.name": "Get Problems", "copilot.tools.readProjectStructure.name": "Project Structure", "copilot.tools.getChangedFiles.name": "Git Changes", "copilot.tools.testFailure.name": "Test Failure", "copilot.tools.updateUserPreferences.name": "Update User Preferences", "copilot.tools.openSimpleBrowser.name": "Open Simple Browser", "copilot.tools.createFile.name": "Create File", "copilot.tools.insertEdit.name": "Edit File", "copilot.tools.replaceString.name": "Replace String in File", "copilot.tools.editNotebook.name": "Edit Notebook", "copilot.tools.runNotebookCell.name": "Run Notebook Cell", "copilot.tools.getNotebookCellOutput.name": "Get Notebook Cell Output", "copilot.tools.fetchWebPage.name": "Fetch Web Page", "copilot.tools.findTestFiles.name": "Find Test Files", "copilot.tools.getDocInfo.name": "Doc Info", "copilot.tools.createDirectory.name": "Create Directory", "github.copilot.config.agent.currentEditorContext.enabled": "When enabled, Copilot will include the name of the current active editor in the context for agent mode.", "github.copilot.config.customInstructionsInSystemMessage": "When enabled, custom instructions and mode instructions will be appended to the system message instead of a user message.", "copilot.toolSet.editing.description": "Edit files in your workspace", "copilot.toolSet.runCommand.description": "Run commands in the terminal", "copilot.toolSet.runNotebook.description": "Run notebook cells", "copilot.toolSet.search.description": "Search and read files in your workspace", "copilot.toolSet.new.description": "Scaffold a new workspace in VS Code", "copilot.toolSet.runTasks.description": "Run tasks in your workspace"}